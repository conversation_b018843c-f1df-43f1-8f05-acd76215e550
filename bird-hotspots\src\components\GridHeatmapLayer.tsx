import { useEffect, useRef, useCallback } from 'react';
import L from 'leaflet';
import { useAppStore } from '../store/useAppStore';
import {
  getGridSize,
  getDisplayThreshold,
  aggregateObservationsToGrid,
  filterGridByBounds,
  limitGridCells,
  getGridStatsText,
  ZOOM_CONFIG,
  isHighDetailMode,
  isUltraDetailMode,
  calculateMapScale,
  filterObservationsByViewport
} from '../utils/gridUtils';
import {
  getColorByCount,
  getSquareOpacity,
  getEnhancedColorMapping,
  getSquareSize
} from '../utils/colorUtils';
import type { GridCell, HeatmapDisplayMode } from '../types';

interface GridHeatmapLayerProps {
  map: L.Map | null;
}

const GridHeatmapLayer = ({ map }: GridHeatmapLayerProps) => {
  const layerGroupRef = useRef<L.LayerGroup | null>(null);
  const currentZoomRef = useRef<number>(5);
  const currentModeRef = useRef<HeatmapDisplayMode>('grid');
  
  const { species, observations, searchQuery, mapZoom } = useAppStore();

  // 清理现有图层
  const clearLayers = useCallback(() => {
    if (layerGroupRef.current && map) {
      if (map.hasLayer(layerGroupRef.current)) {
        map.removeLayer(layerGroupRef.current);
      }
      layerGroupRef.current.clearLayers();
    }
  }, [map]);

  // 创建网格矩形
  const createGridRectangle = useCallback((gridCell: GridCell, _speciesName: string): L.Rectangle => {
    const bounds: L.LatLngBoundsExpression = [
      [gridCell.bounds.south, gridCell.bounds.west],
      [gridCell.bounds.north, gridCell.bounds.east]
    ];

    const color = getColorByCount(gridCell.totalCount);
    const opacity = getSquareOpacity(gridCell.totalCount);

    const rectangle = L.rectangle(bounds, {
      fillColor: color,
      fillOpacity: opacity,
      color: '#ffffff',
      weight: 1,
      opacity: 0.8
    });

    // 添加弹窗
    rectangle.bindPopup(getGridStatsText(gridCell), {
      maxWidth: 250,
      className: 'grid-popup'
    });

    // 添加悬停效果
    rectangle.on('mouseover', function(this: L.Rectangle) {
      this.setStyle({
        weight: 2,
        opacity: 1,
        fillOpacity: Math.min(opacity + 0.2, 1)
      });
    });

    rectangle.on('mouseout', function(this: L.Rectangle) {
      this.setStyle({
        weight: 1,
        opacity: 0.8,
        fillOpacity: opacity
      });
    });

    return rectangle;
  }, []);

  // 创建点标记（高缩放级别时使用）- 使用标准水滴图标
  const createPointMarker = useCallback((obs: any, speciesItem: any, zoomLevel: number): L.Marker => {
    const isHighDetail = isHighDetailMode(zoomLevel);

    // 使用增强的颜色映射
    const colorMapping = getEnhancedColorMapping(obs.count, obs.intensity);
    const pointSize = getSquareSize(obs.count, zoomLevel);

    // 根据详细程度调整样式
    const borderWidth = isHighDetail ? 2 : 1;
    const shadowIntensity = isHighDetail ? 0.4 : 0.3;

    // 创建标准水滴形状的SVG图标
    const createDropletSVG = (color: string, borderColor: string, size: number) => {
      return `data:image/svg+xml;base64,${btoa(`
        <svg width="${size}" height="${size * 1.2}" viewBox="0 0 24 29" xmlns="http://www.w3.org/2000/svg">
          <path d="M12 0C12 0 0 12 0 18C0 24.627 5.373 30 12 30C18.627 30 24 24.627 24 18C24 12 12 0 12 0Z"
                fill="${color}"
                stroke="${borderColor}"
                stroke-width="${borderWidth}"
                opacity="${colorMapping.opacity}"
                filter="drop-shadow(0 2px 4px rgba(0,0,0,${shadowIntensity}))"/>
          <circle cx="12" cy="18" r="2" fill="rgba(255,255,255,0.9)" opacity="0.8"/>
        </svg>
      `)}`;
    };

    const dropletIcon = L.icon({
      iconUrl: createDropletSVG(colorMapping.color, colorMapping.borderColor, pointSize),
      iconSize: [pointSize, pointSize * 1.2],
      iconAnchor: [pointSize / 2, pointSize * 1.2],
      popupAnchor: [0, -pointSize * 1.2],
      className: 'observation-point-marker standard-droplet-marker'
    });

    const marker = L.marker([obs.lat, obs.lng], {
      icon: dropletIcon,
      riseOnHover: true
    });

    // 增强的悬停效果（适用于标准水滴图标）
    marker.on('mouseover', function(this: L.Marker) {
      const element = this.getElement();
      if (element) {
        const img = element.querySelector('img');
        if (img) {
          img.style.transform = 'scale(1.3)';
          img.style.filter = `drop-shadow(0 4px 8px rgba(0,0,0,${shadowIntensity + 0.2})) brightness(1.1) saturate(1.2)`;
          img.style.zIndex = '1000';
          img.style.transition = 'all 0.15s cubic-bezier(0.4, 0, 0.2, 1)';
        }
      }
    });

    marker.on('mouseout', function(this: L.Marker) {
      const element = this.getElement();
      if (element) {
        const img = element.querySelector('img');
        if (img) {
          img.style.transform = 'scale(1)';
          img.style.filter = `drop-shadow(0 2px 4px rgba(0,0,0,${shadowIntensity}))`;
          img.style.zIndex = 'auto';
          img.style.transition = 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)';
        }
      }
    });

    // 增强的弹窗信息，包含详细坐标和属性信息
    const currentDate = new Date().toLocaleDateString('zh-CN');
    const mapScale = calculateMapScale(zoomLevel, obs.lat);

    marker.bindPopup(`
      <div class="text-sm p-4 min-w-[280px] max-w-[320px]">
        <div class="border-b pb-3 mb-3">
          <div class="font-bold text-gray-800 text-base mb-1">${speciesItem.name}</div>
          <div class="text-gray-600 italic text-sm">${speciesItem.scientificName}</div>
        </div>

        <div class="space-y-3">
          <div class="grid grid-cols-2 gap-3">
            <div class="bg-blue-50 p-2 rounded">
              <div class="text-xs text-gray-500 mb-1">观察数量</div>
              <div class="font-bold text-blue-600 text-lg">${obs.count}</div>
            </div>
            <div class="bg-green-50 p-2 rounded">
              <div class="text-xs text-gray-500 mb-1">观察强度</div>
              <div class="font-bold text-green-600 text-lg">${(obs.intensity * 100).toFixed(1)}%</div>
            </div>
          </div>

          <div class="border-t pt-3">
            <div class="text-xs text-gray-500 mb-2 font-medium">精确坐标信息</div>
            <div class="space-y-1">
              <div class="flex items-center justify-between text-xs">
                <span class="text-gray-600">纬度:</span>
                <span class="font-mono text-gray-800 bg-gray-100 px-2 py-1 rounded">${obs.lat.toFixed(6)}°</span>
              </div>
              <div class="flex items-center justify-between text-xs">
                <span class="text-gray-600">经度:</span>
                <span class="font-mono text-gray-800 bg-gray-100 px-2 py-1 rounded">${obs.lng.toFixed(6)}°</span>
              </div>
              <div class="flex items-center justify-between text-xs">
                <span class="text-gray-600">缩放级别:</span>
                <span class="font-mono text-gray-800">${zoomLevel}</span>
              </div>
              <div class="flex items-center justify-between text-xs">
                <span class="text-gray-600">地图比例尺:</span>
                <span class="font-mono text-gray-800">1:${mapScale.toLocaleString()}</span>
              </div>
            </div>
          </div>

          <div class="border-t pt-2">
            <div class="text-xs text-gray-400 text-center">
              数据更新时间: ${currentDate}
            </div>
          </div>
        </div>
      </div>
    `, {
      maxWidth: 350,
      className: 'enhanced-observation-popup',
      closeButton: true,
      autoPan: true
    });

    return marker;
  }, []);

  // 渲染网格热力图
  const renderGridHeatmap = useCallback(() => {
    if (!map) return;

    clearLayers();

    // 创建新的图层组
    const layerGroup = L.layerGroup();
    layerGroupRef.current = layerGroup;

    const currentZoom = map.getZoom();
    const mapCenter = map.getCenter();
    const displayMode = getDisplayThreshold(currentZoom, mapCenter.lat);
    const gridSize = getGridSize(currentZoom);
    // const detailLevel = getDetailLevel(currentZoom); // 暂时未使用

    // 过滤可见物种
    const filteredSpecies = species.filter(speciesItem => {
      if (!speciesItem.isVisible) return false;

      if (searchQuery.trim()) {
        const query = searchQuery.toLowerCase();
        return speciesItem.name.toLowerCase().includes(query) ||
               speciesItem.scientificName.toLowerCase().includes(query);
      }

      return true;
    });

    if (displayMode === 'points') {
      // 高缩放级别：显示具体观察点（方块形状）
      const mapBounds = {
        north: map.getBounds().getNorth(),
        south: map.getBounds().getSouth(),
        east: map.getBounds().getEast(),
        west: map.getBounds().getWest()
      };

      const isHighDetail = isHighDetailMode(currentZoom);
      const isUltraDetail = isUltraDetailMode(currentZoom);

      // 根据详细程度确定最大点数
      let maxPoints = ZOOM_CONFIG.MAX_POINTS_DISPLAY;
      if (isUltraDetail) {
        maxPoints = ZOOM_CONFIG.MAX_POINTS_ULTRA_DETAIL;
      } else if (isHighDetail) {
        maxPoints = ZOOM_CONFIG.MAX_POINTS_HIGH_DETAIL;
      }

      filteredSpecies.forEach(speciesItem => {
        let speciesObservations = observations.filter(
          obs => obs.speciesId === speciesItem.id
        );

        // 使用增强的视窗过滤和优先级排序
        if (speciesObservations.length > 0) {
          const { inViewport, nearViewport } = filterObservationsByViewport(
            speciesObservations,
            mapBounds,
            [mapCenter.lat, mapCenter.lng],
            ZOOM_CONFIG.VIEWPORT_PRIORITY_POINTS
          );

          // 优先显示视窗内的点，然后是附近的点
          const prioritizedObservations = [...inViewport, ...nearViewport];
          const maxPointsPerSpecies = Math.ceil(maxPoints / filteredSpecies.length);

          const finalObservations = prioritizedObservations.slice(0, maxPointsPerSpecies);

          finalObservations.forEach(obs => {
            const marker = createPointMarker(obs, speciesItem, currentZoom);
            layerGroup.addLayer(marker);
          });
        }
      });
    } else {
      // 低缩放级别：显示网格聚合
      const visibleSpeciesIds = filteredSpecies.map(s => s.id);
      const gridMap = aggregateObservationsToGrid(observations, gridSize, visibleSpeciesIds);

      // 获取地图边界
      const bounds = map.getBounds();
      const mapBounds = {
        north: bounds.getNorth(),
        south: bounds.getSouth(),
        east: bounds.getEast(),
        west: bounds.getWest()
      };

      // 过滤可见网格
      const visibleGrid = filterGridByBounds(gridMap, mapBounds);
      
      // 限制网格数量以提高性能
      const mapCenter = map.getCenter();
      const limitedGrid = limitGridCells(visibleGrid, 1000, [mapCenter.lat, mapCenter.lng]);

      // 创建网格矩形
      limitedGrid.forEach(gridCell => {
        if (gridCell.totalCount > 0) {
          const rectangle = createGridRectangle(gridCell, '多物种聚合');
          layerGroup.addLayer(rectangle);
        }
      });
    }

    // 添加到地图
    layerGroup.addTo(map);
    
    // 更新当前状态
    currentZoomRef.current = currentZoom;
    currentModeRef.current = displayMode;
  }, [map, species, observations, searchQuery, clearLayers, createGridRectangle, createPointMarker]);

  // 处理缩放变化
  const handleZoomChange = useCallback(() => {
    if (!map) return;
    
    const newZoom = map.getZoom();
    const newMode = getDisplayThreshold(newZoom);
    
    // 只有当缩放级别变化足够大或显示模式改变时才重新渲染
    if (Math.abs(newZoom - currentZoomRef.current) >= 1 || newMode !== currentModeRef.current) {
      renderGridHeatmap();
    }
  }, [map, renderGridHeatmap]);

  // 处理地图移动
  const handleMapMove = useCallback(() => {
    if (!map) return;
    
    // 在网格模式下，地图移动时需要重新渲染以显示新区域的网格
    if (currentModeRef.current === 'grid') {
      renderGridHeatmap();
    }
  }, [map, renderGridHeatmap]);

  // 主要效果：监听数据变化和地图事件
  useEffect(() => {
    if (!map) return;

    // 初始渲染
    renderGridHeatmap();

    // 添加事件监听器
    map.on('zoomend', handleZoomChange);
    map.on('moveend', handleMapMove);

    // 清理函数
    return () => {
      map.off('zoomend', handleZoomChange);
      map.off('moveend', handleMapMove);
      clearLayers();
    };
  }, [map, species, observations, searchQuery, renderGridHeatmap, handleZoomChange, handleMapMove, clearLayers]);

  // 监听缩放状态变化（来自store）
  useEffect(() => {
    if (map && mapZoom !== currentZoomRef.current) {
      handleZoomChange();
    }
  }, [map, mapZoom, handleZoomChange]);

  return null; // 这个组件不渲染任何DOM元素
};

export default GridHeatmapLayer;
