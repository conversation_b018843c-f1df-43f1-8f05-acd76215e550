/**
 * 颜色映射工具函数
 * 用于根据观察数量生成颜色梯度
 */

export interface ColorRange {
  min: number;
  max: number;
  color: string;
  label: string;
}

/**
 * 预定义的颜色范围配置
 * 参考 eBird 网站的设计风格
 */
export const COLOR_RANGES: ColorRange[] = [
  { min: 0, max: 15, color: '#e8f5e8', label: '0-15' },
  { min: 16, max: 50, color: '#c8e6c9', label: '16-50' },
  { min: 51, max: 100, color: '#a5d6a7', label: '51-100' },
  { min: 101, max: 150, color: '#81c784', label: '101-150' },
  { min: 151, max: 200, color: '#66bb6a', label: '151-200' },
  { min: 201, max: 300, color: '#4caf50', label: '201-300' },
  { min: 301, max: 400, color: '#43a047', label: '301-400' },
  { min: 401, max: 500, color: '#388e3c', label: '401-500' },
  { min: 501, max: 600, color: '#2e7d32', label: '501-600' },
  { min: 601, max: Infinity, color: '#1b5e20', label: '600+' }
];

/**
 * 替代颜色方案 - 蓝色系
 */
export const BLUE_COLOR_RANGES: ColorRange[] = [
  { min: 0, max: 15, color: '#e3f2fd', label: '0-15' },
  { min: 16, max: 50, color: '#bbdefb', label: '16-50' },
  { min: 51, max: 100, color: '#90caf9', label: '51-100' },
  { min: 101, max: 150, color: '#64b5f6', label: '101-150' },
  { min: 151, max: 200, color: '#42a5f5', label: '151-200' },
  { min: 201, max: 300, color: '#2196f3', label: '201-300' },
  { min: 301, max: 400, color: '#1e88e5', label: '301-400' },
  { min: 401, max: 500, color: '#1976d2', label: '401-500' },
  { min: 501, max: 600, color: '#1565c0', label: '501-600' },
  { min: 601, max: Infinity, color: '#0d47a1', label: '600+' }
];

/**
 * 替代颜色方案 - 橙红色系
 */
export const ORANGE_COLOR_RANGES: ColorRange[] = [
  { min: 0, max: 15, color: '#fff3e0', label: '0-15' },
  { min: 16, max: 50, color: '#ffe0b2', label: '16-50' },
  { min: 51, max: 100, color: '#ffcc80', label: '51-100' },
  { min: 101, max: 150, color: '#ffb74d', label: '101-150' },
  { min: 151, max: 200, color: '#ffa726', label: '151-200' },
  { min: 201, max: 300, color: '#ff9800', label: '201-300' },
  { min: 301, max: 400, color: '#fb8c00', label: '301-400' },
  { min: 401, max: 500, color: '#f57c00', label: '401-500' },
  { min: 501, max: 600, color: '#ef6c00', label: '501-600' },
  { min: 601, max: Infinity, color: '#e65100', label: '600+' }
];

/**
 * 根据观察数量获取对应的颜色
 * @param count 观察数量
 * @param colorRanges 颜色范围配置，默认使用绿色系
 * @returns 对应的颜色值
 */
export function getColorByCount(count: number, colorRanges: ColorRange[] = COLOR_RANGES): string {
  const range = colorRanges.find(range => count >= range.min && count <= range.max);
  return range ? range.color : colorRanges[colorRanges.length - 1].color;
}

/**
 * 根据观察数量获取对应的颜色范围信息
 * @param count 观察数量
 * @param colorRanges 颜色范围配置
 * @returns 对应的颜色范围对象
 */
export function getColorRangeByCount(count: number, colorRanges: ColorRange[] = COLOR_RANGES): ColorRange {
  const range = colorRanges.find(range => count >= range.min && count <= range.max);
  return range || colorRanges[colorRanges.length - 1];
}

/**
 * 计算水滴标记的大小（用于坐标点显示）
 * @param count 观察数量
 * @param zoomLevel 缩放级别
 * @param minSize 最小尺寸（像素）
 * @param maxSize 最大尺寸（像素）
 * @returns 水滴标记的尺寸
 */
export function getSquareSize(
  count: number,
  zoomLevel: number = 15,
  minSize: number = 12,
  maxSize: number = 32
): number {
  // 根据缩放级别调整基础大小，增强缩放效果
  const zoomFactor = Math.max(0.6, Math.min(2.5, (zoomLevel - 10) / 8));
  const adjustedMinSize = Math.round(minSize * zoomFactor);
  const adjustedMaxSize = Math.round(maxSize * zoomFactor);

  // 使用对数刻度来计算大小，避免极值过于突出
  const logCount = Math.log10(Math.max(1, count));
  const maxLogCount = Math.log10(1000); // 假设最大值为1000
  const ratio = Math.min(logCount / maxLogCount, 1);

  return Math.round(adjustedMinSize + (adjustedMaxSize - adjustedMinSize) * ratio);
}

/**
 * 计算方块的大小（用于热力图网格显示）
 * @param count 观察数量
 * @param minSize 最小尺寸（像素）
 * @param maxSize 最大尺寸（像素）
 * @returns 方块的尺寸
 */
export function getGridSquareSize(count: number, minSize: number = 8, maxSize: number = 24): number {
  // 使用对数刻度来计算大小，避免极值过于突出
  const logCount = Math.log10(Math.max(1, count));
  const maxLogCount = Math.log10(1000); // 假设最大值为1000
  const ratio = Math.min(logCount / maxLogCount, 1);

  return Math.round(minSize + (maxSize - minSize) * ratio);
}

/**
 * 获取方块的透明度
 * @param count 观察数量
 * @param minOpacity 最小透明度
 * @param maxOpacity 最大透明度
 * @returns 透明度值
 */
export function getSquareOpacity(count: number, minOpacity: number = 0.6, maxOpacity: number = 0.9): number {
  const logCount = Math.log10(Math.max(1, count));
  const maxLogCount = Math.log10(1000);
  const ratio = Math.min(logCount / maxLogCount, 1);
  
  return minOpacity + (maxOpacity - minOpacity) * ratio;
}

/**
 * 生成方块热点的 CSS 类名
 * @param count 观察数量
 * @param zoomLevel 缩放级别
 * @returns CSS 类名
 */
export function getSquareClassName(count: number, zoomLevel: number = 15): string {
  const size = getSquareSize(count, zoomLevel);
  return `hotspot-square hotspot-size-${size}`;
}

/**
 * 获取增强的颜色深度映射（用于坐标点显示）
 * @param count 观察数量
 * @param intensity 强度值（0-1）
 * @param colorRanges 颜色范围配置
 * @returns 包含颜色和透明度的对象
 */
export function getEnhancedColorMapping(
  count: number,
  intensity: number = 0.5,
  colorRanges: ColorRange[] = COLOR_RANGES
): { color: string; opacity: number; borderColor: string } {
  const baseColor = getColorByCount(count, colorRanges);
  const baseOpacity = getSquareOpacity(count);

  // 根据强度调整透明度
  const adjustedOpacity = Math.max(0.4, Math.min(1, baseOpacity * (0.7 + intensity * 0.3)));

  // 生成边框颜色（比填充颜色稍深）
  const borderColor = darkenColor(baseColor, 0.2);

  return {
    color: baseColor,
    opacity: adjustedOpacity,
    borderColor
  };
}

/**
 * 使颜色变深
 * @param color 十六进制颜色值
 * @param factor 变深因子（0-1）
 * @returns 变深后的颜色
 */
export function darkenColor(color: string, factor: number): string {
  // 移除 # 符号
  const hex = color.replace('#', '');

  // 解析 RGB 值
  const r = parseInt(hex.substr(0, 2), 16);
  const g = parseInt(hex.substr(2, 2), 16);
  const b = parseInt(hex.substr(4, 2), 16);

  // 应用变深因子
  const newR = Math.round(r * (1 - factor));
  const newG = Math.round(g * (1 - factor));
  const newB = Math.round(b * (1 - factor));

  // 转换回十六进制
  return `#${newR.toString(16).padStart(2, '0')}${newG.toString(16).padStart(2, '0')}${newB.toString(16).padStart(2, '0')}`;
}

/**
 * 根据数据密度获取颜色（浅色=数量少，深色=数量多）
 * @param count 观察数量
 * @param maxCount 最大观察数量（用于归一化）
 * @param colorScheme 颜色方案
 * @returns 颜色值
 */
export function getColorByDensity(
  count: number,
  maxCount: number = 1000,
  colorScheme: 'green' | 'blue' | 'heat' = 'green'
): string {
  const ratio = Math.min(count / maxCount, 1);

  switch (colorScheme) {
    case 'blue':
      return interpolateColor('#e3f2fd', '#0d47a1', ratio);
    case 'heat':
      return interpolateColor('#fff5f5', '#7f1d1d', ratio);
    case 'green':
    default:
      return interpolateColor('#f0fdf4', '#14532d', ratio);
  }
}

/**
 * 颜色插值
 * @param color1 起始颜色
 * @param color2 结束颜色
 * @param ratio 插值比例（0-1）
 * @returns 插值后的颜色
 */
export function interpolateColor(color1: string, color2: string, ratio: number): string {
  const hex1 = color1.replace('#', '');
  const hex2 = color2.replace('#', '');

  const r1 = parseInt(hex1.substr(0, 2), 16);
  const g1 = parseInt(hex1.substr(2, 2), 16);
  const b1 = parseInt(hex1.substr(4, 2), 16);

  const r2 = parseInt(hex2.substr(0, 2), 16);
  const g2 = parseInt(hex2.substr(2, 2), 16);
  const b2 = parseInt(hex2.substr(4, 2), 16);

  const r = Math.round(r1 + (r2 - r1) * ratio);
  const g = Math.round(g1 + (g2 - g1) * ratio);
  const b = Math.round(b1 + (b2 - b1) * ratio);

  return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
}
